// ==UserScript==
// @name         飞书自动回复助手 (界面版)
// @namespace    http://tampermonkey.net/
// @version      3.2.1
// @description  自动监控飞书消息并智能回复所有未读消息 - 支持图形界面配置，精准免打扰检测，智能识别用户自己的消息，修复重复回复问题
// <AUTHOR>
// @match        https://hqeeyh9oln3.feishu.cn/next/messenger/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    console.log('飞书自动回复助手 (界面版) v3.2.1 已启动');
    console.log('v3.2.1 更新: 修复重复回复问题，增加安全保护机制，优化消息识别逻辑');

    // 配置管理器
    class ConfigManager {
        constructor() {
            this.storageKey = 'feishu_auto_reply_config';
            this.defaultConfig = {
                // 输入框元素选择器
                inputBoxSelector: '[contenteditable="true"]',
                // 发送按钮选择器（多种备选方案）
                sendButtonSelectors: [
                    '.universe-icon.send__button.send-colorful',
                    '.send__button',
                    'button[type="submit"]',
                    '[class*="send"]'
                ],
                // 回复内容配置
                replyMessages: {
                    default: '您好！我已收到您的消息，感谢您的联系。',
                    group: '您好！我已收到群聊消息，感谢大家的分享。',
                    security: '您好！我已收到您的安全提醒消息，谢谢您的关注。'
                },
                // 跳过的对话关键词
                skipConversations: ['账号安全中心'],
                // 检查间隔（毫秒）
                checkInterval: 1500,
                // 操作延迟
                actionDelay: 1000,
                // 是否启用自动回复
                enabled: true,
                // 是否显示通知
                showNotifications: true,
                // 安全保护参数
                maxRepliesPerConversation: 1, // 单对话最大回复次数
                minReplyInterval: 30000, // 最小回复间隔（毫秒）
                emergencyStopThreshold: 5 // 紧急停止阈值
            };
            this.config = this.loadConfig();
        }

        // 加载配置
        loadConfig() {
            try {
                const saved = localStorage.getItem(this.storageKey);
                if (saved) {
                    const parsed = JSON.parse(saved);
                    return { ...this.defaultConfig, ...parsed };
                }
            } catch (error) {
                console.error('加载配置失败:', error);
            }
            return { ...this.defaultConfig };
        }

        // 保存配置
        saveConfig(newConfig = null) {
            try {
                const configToSave = newConfig || this.config;
                localStorage.setItem(this.storageKey, JSON.stringify(configToSave));
                if (newConfig) {
                    this.config = { ...this.config, ...newConfig };
                }
                console.log('配置已保存');
                return true;
            } catch (error) {
                console.error('保存配置失败:', error);
                return false;
            }
        }

        // 获取配置
        getConfig() {
            return { ...this.config };
        }

        // 更新配置
        updateConfig(updates) {
            this.config = { ...this.config, ...updates };
            return this.saveConfig();
        }

        // 重置为默认配置
        resetToDefault() {
            this.config = { ...this.defaultConfig };
            return this.saveConfig();
        }

        // 验证配置
        validateConfig(config) {
            const errors = [];

            if (!config.inputBoxSelector || typeof config.inputBoxSelector !== 'string') {
                errors.push('输入框选择器不能为空');
            }

            if (!Array.isArray(config.sendButtonSelectors) || config.sendButtonSelectors.length === 0) {
                errors.push('发送按钮选择器至少需要一个');
            }

            if (config.checkInterval < 500) {
                errors.push('检查间隔不能少于500毫秒');
            }

            if (config.actionDelay < 100) {
                errors.push('操作延迟不能少于100毫秒');
            }

            return errors;
        }

        // 导出配置
        exportConfig() {
            return JSON.stringify(this.config, null, 2);
        }

        // 导入配置
        importConfig(configString) {
            try {
                const imported = JSON.parse(configString);
                const errors = this.validateConfig(imported);
                if (errors.length > 0) {
                    throw new Error('配置验证失败: ' + errors.join(', '));
                }
                this.config = { ...this.defaultConfig, ...imported };
                this.saveConfig();
                return true;
            } catch (error) {
                console.error('导入配置失败:', error);
                throw error;
            }
        }
    }

    // UI管理器
    class UIManager {
        constructor(configManager) {
            this.configManager = configManager;
            this.isVisible = false;
            this.container = null;
            this.createUI();
            this.bindEvents();
            this.bindQuickToggleEvent();
        }

        // 创建配置界面
        createUI() {
            // 创建样式
            this.createStyles();

            // 创建主容器
            this.container = document.createElement('div');
            this.container.id = 'feishu-config-panel';
            this.container.className = 'feishu-config-hidden';

            this.container.innerHTML = `
                <div class="feishu-config-overlay">
                    <div class="feishu-config-modal">
                        <div class="feishu-config-header">
                            <h2>飞书自动回复助手 - 配置</h2>
                            <button class="feishu-config-close" title="关闭">×</button>
                        </div>

                        <div class="feishu-config-tabs">
                            <button class="feishu-config-tab active" data-tab="messages">回复消息</button>
                            <button class="feishu-config-tab" data-tab="advanced">高级设置</button>
                            <button class="feishu-config-tab" data-tab="import-export">导入导出</button>
                        </div>

                        <div class="feishu-config-content">
                             <!-- 快捷控制 -->
                             <div class="feishu-config-quick-control">
                                 <div class="feishu-config-group">
                                     <label class="feishu-config-toggle">
                                         <input type="checkbox" id="quickEnabled" />
                                         <span class="feishu-config-toggle-slider"></span>
                                         <span class="feishu-config-toggle-label">启用自动回复</span>
                                     </label>
                                 </div>
                             </div>

                             <!-- 回复消息 -->
                             <div class="feishu-config-tab-content active" data-tab="messages">
                                <div class="feishu-config-group">
                                    <label>默认回复消息</label>
                                    <textarea id="replyDefault" rows="3" placeholder="默认回复内容"></textarea>
                                </div>

                                <div class="feishu-config-group">
                                    <label>群聊回复消息</label>
                                    <textarea id="replyGroup" rows="3" placeholder="群聊回复内容"></textarea>
                                </div>

                                <div class="feishu-config-group">
                                    <label>安全提醒回复消息</label>
                                    <textarea id="replySecurity" rows="3" placeholder="安全提醒回复内容"></textarea>
                                </div>
                            </div>

                            <!-- 高级设置 -->
                            <div class="feishu-config-tab-content" data-tab="advanced">
                                <div class="feishu-config-group">
                                    <label>检查间隔 (毫秒)</label>
                                    <input type="number" id="checkInterval" min="500" step="250" />
                                    <small>建议值: 3000-5000毫秒</small>
                                </div>

                                <div class="feishu-config-group">
                                    <label>操作延迟 (毫秒)</label>
                                    <input type="number" id="actionDelay" min="100" step="100" />
                                    <small>建议值: 1000-2000毫秒</small>
                                </div>
                            </div>

                            <!-- 导入导出 -->
                            <div class="feishu-config-tab-content" data-tab="import-export">
                                <div class="feishu-config-group">
                                    <label>导出配置</label>
                                    <button id="exportConfig" class="feishu-config-btn">导出配置到剪贴板</button>
                                </div>

                                <div class="feishu-config-group">
                                    <label>导入配置</label>
                                    <textarea id="importConfig" rows="8" placeholder="粘贴配置JSON到这里"></textarea>
                                    <button id="importConfigBtn" class="feishu-config-btn">导入配置</button>
                                </div>

                                <div class="feishu-config-group">
                                    <button id="resetConfig" class="feishu-config-btn feishu-config-btn-danger">重置为默认配置</button>
                                </div>
                            </div>
                        </div>

                        <div class="feishu-config-footer">
                            <button id="saveConfig" class="feishu-config-btn feishu-config-btn-primary">保存配置</button>
                            <button id="cancelConfig" class="feishu-config-btn">取消</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(this.container);
        }

        // 创建样式
        createStyles() {
            const style = document.createElement('style');
            style.textContent = `
                .feishu-config-hidden {
                    display: none !important;
                }

                #feishu-config-panel {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 999999;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                }

                .feishu-config-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .feishu-config-modal {
                    background: white;
                    border-radius: 8px;
                    width: 90%;
                    max-width: 800px;
                    max-height: 90%;
                    overflow: hidden;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    display: flex;
                    flex-direction: column;
                }

                .feishu-config-header {
                    padding: 20px;
                    border-bottom: 1px solid #e5e5e5;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    background: #f8f9fa;
                }

                .feishu-config-header h2 {
                    margin: 0;
                    color: #333;
                    font-size: 18px;
                }

                .feishu-config-close {
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                    padding: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .feishu-config-close:hover {
                    color: #333;
                }

                .feishu-config-tabs {
                    display: flex;
                    border-bottom: 1px solid #e5e5e5;
                    background: #f8f9fa;
                }

                .feishu-config-tab {
                    padding: 12px 20px;
                    border: none;
                    background: none;
                    cursor: pointer;
                    color: #666;
                    font-size: 14px;
                    border-bottom: 2px solid transparent;
                }

                .feishu-config-tab.active {
                    color: #007bff;
                    border-bottom-color: #007bff;
                }

                .feishu-config-tab:hover {
                    color: #007bff;
                }

                .feishu-config-content {
                    flex: 1;
                    overflow-y: auto;
                    padding: 20px;
                }

                .feishu-config-tab-content {
                    display: none;
                }

                .feishu-config-tab-content.active {
                    display: block;
                }

                .feishu-config-group {
                    margin-bottom: 20px;
                }

                .feishu-config-group label {
                    display: block;
                    margin-bottom: 8px;
                    font-weight: 500;
                    color: #333;
                }

                .feishu-config-quick-control {
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 15px;
                    margin-bottom: 20px;
                    border-left: 4px solid #007bff;
                }

                .feishu-config-toggle {
                    display: flex !important;
                    align-items: center;
                    cursor: pointer;
                    margin-bottom: 0 !important;
                }

                .feishu-config-toggle input[type="checkbox"] {
                    display: none;
                }

                .feishu-config-toggle-slider {
                    position: relative;
                    width: 50px;
                    height: 24px;
                    background-color: #ccc;
                    border-radius: 24px;
                    transition: background-color 0.3s;
                    margin-right: 10px;
                }

                .feishu-config-toggle-slider:before {
                    content: '';
                    position: absolute;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background-color: white;
                    top: 2px;
                    left: 2px;
                    transition: transform 0.3s;
                }

                .feishu-config-toggle input:checked + .feishu-config-toggle-slider {
                    background-color: #007bff;
                }

                .feishu-config-toggle input:checked + .feishu-config-toggle-slider:before {
                    transform: translateX(26px);
                }

                .feishu-config-toggle-label {
                    font-weight: bold;
                    color: #333;
                    font-size: 16px;
                }

                .feishu-config-group input,
                .feishu-config-group textarea {
                    width: 100%;
                    padding: 8px 12px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    font-size: 14px;
                    box-sizing: border-box;
                }

                .feishu-config-group input[type="checkbox"] {
                    width: auto;
                    margin-right: 8px;
                }

                .feishu-config-group small {
                    display: block;
                    margin-top: 4px;
                    color: #666;
                    font-size: 12px;
                }

                .feishu-config-btn {
                    padding: 8px 16px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    background: white;
                    cursor: pointer;
                    font-size: 14px;
                    margin-right: 8px;
                }

                .feishu-config-btn:hover {
                    background: #f8f9fa;
                }

                .feishu-config-btn-primary {
                    background: #007bff;
                    color: white;
                    border-color: #007bff;
                }

                .feishu-config-btn-primary:hover {
                    background: #0056b3;
                }

                .feishu-config-btn-danger {
                    background: #dc3545;
                    color: white;
                    border-color: #dc3545;
                }

                .feishu-config-btn-danger:hover {
                    background: #c82333;
                }

                .feishu-config-footer {
                    padding: 20px;
                    border-top: 1px solid #e5e5e5;
                    background: #f8f9fa;
                    text-align: right;
                }

                .feishu-config-trigger {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #007bff;
                    color: white;
                    border: none;
                    border-radius: 50%;
                    width: 50px;
                    height: 50px;
                    cursor: pointer;
                    font-size: 18px;
                    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
                    z-index: 999998;
                }

                .feishu-config-trigger:hover {
                    background: #0056b3;
                    transform: scale(1.05);
                }
            `;
            document.head.appendChild(style);
        }

        // 绑定事件
        bindEvents() {
            // 创建触发按钮
            const trigger = document.createElement('button');
            trigger.className = 'feishu-config-trigger';
            trigger.innerHTML = '⚙️';
            trigger.title = '打开配置面板 (Ctrl+Shift+C)';
            trigger.onclick = () => this.show();
            document.body.appendChild(trigger);

            // 键盘快捷键
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey && e.shiftKey && e.code === 'KeyC') {
                    e.preventDefault();
                    this.toggle();
                }
            });

            // 关闭按钮
            this.container.querySelector('.feishu-config-close').onclick = () => this.hide();
            this.container.querySelector('#cancelConfig').onclick = () => this.hide();

            // 点击遮罩关闭
            this.container.querySelector('.feishu-config-overlay').onclick = (e) => {
                if (e.target === e.currentTarget) {
                    this.hide();
                }
            };

            // 标签页切换
            this.container.querySelectorAll('.feishu-config-tab').forEach(tab => {
                tab.onclick = () => this.switchTab(tab.dataset.tab);
            });

            // 保存配置
            this.container.querySelector('#saveConfig').onclick = () => this.saveConfig();

            // 导出配置
            this.container.querySelector('#exportConfig').onclick = () => this.exportConfig();

            // 导入配置
            this.container.querySelector('#importConfigBtn').onclick = () => this.importConfig();

            // 重置配置
            this.container.querySelector('#resetConfig').onclick = () => this.resetConfig();
        }

        // 绑定快捷开关事件
        bindQuickToggleEvent() {
            const quickToggle = this.container.querySelector('#quickEnabled');
            quickToggle.addEventListener('change', (e) => {
                const enabled = e.target.checked;
                this.configManager.updateConfig({ enabled });
                Object.assign(CONFIG, this.configManager.getConfig());
                console.log(`自动回复已${enabled ? '启用' : '禁用'}`);
            });
        }

        // 显示配置面板
        show() {
            this.loadConfigToUI();
            this.container.classList.remove('feishu-config-hidden');
            this.isVisible = true;
        }

        // 隐藏配置面板
        hide() {
            this.container.classList.add('feishu-config-hidden');
            this.isVisible = false;
        }

        // 切换显示状态
        toggle() {
            if (this.isVisible) {
                this.hide();
            } else {
                this.show();
            }
        }

        // 切换标签页
        switchTab(tabName) {
            // 切换标签按钮状态
            this.container.querySelectorAll('.feishu-config-tab').forEach(tab => {
                tab.classList.toggle('active', tab.dataset.tab === tabName);
            });

            // 切换内容区域
            this.container.querySelectorAll('.feishu-config-tab-content').forEach(content => {
                content.classList.toggle('active', content.dataset.tab === tabName);
            });
        }

        // 加载配置到UI
        loadConfigToUI() {
            const config = this.configManager.getConfig();

            // 快捷控制
            this.container.querySelector('#quickEnabled').checked = config.enabled;

            // 回复消息
            this.container.querySelector('#replyDefault').value = config.replyMessages.default;
            this.container.querySelector('#replyGroup').value = config.replyMessages.group;
            this.container.querySelector('#replySecurity').value = config.replyMessages.security;

            // 高级设置
            this.container.querySelector('#checkInterval').value = config.checkInterval;
            this.container.querySelector('#actionDelay').value = config.actionDelay;
        }

        // 从UI收集配置
        collectConfigFromUI() {
            const currentConfig = this.configManager.getConfig();
            return {
                // 允许修改启用状态
                enabled: this.container.querySelector('#quickEnabled').checked,
                // 保持其他基本配置不变
                showNotifications: currentConfig.showNotifications,
                inputBoxSelector: currentConfig.inputBoxSelector,
                sendButtonSelectors: currentConfig.sendButtonSelectors,
                skipConversations: currentConfig.skipConversations,
                // 允许修改回复消息和高级设置
                replyMessages: {
                    default: this.container.querySelector('#replyDefault').value.trim(),
                    group: this.container.querySelector('#replyGroup').value.trim(),
                    security: this.container.querySelector('#replySecurity').value.trim()
                },
                checkInterval: parseInt(this.container.querySelector('#checkInterval').value),
                actionDelay: parseInt(this.container.querySelector('#actionDelay').value)
            };
        }

        // 保存配置
        saveConfig() {
            try {
                const newConfig = this.collectConfigFromUI();
                const errors = this.configManager.validateConfig(newConfig);

                if (errors.length > 0) {
                    alert('配置验证失败:\n' + errors.join('\n'));
                    return;
                }

                if (this.configManager.updateConfig(newConfig)) {
                    alert('配置保存成功！');
                    this.hide();
                    // 重新加载配置
                    Object.assign(CONFIG, this.configManager.getConfig());
                } else {
                    alert('配置保存失败！');
                }
            } catch (error) {
                alert('保存配置时出错: ' + error.message);
            }
        }

        // 导出配置
        exportConfig() {
            try {
                const configString = this.configManager.exportConfig();
                navigator.clipboard.writeText(configString).then(() => {
                    alert('配置已复制到剪贴板！');
                }).catch(() => {
                    // 备用方案
                    const textarea = document.createElement('textarea');
                    textarea.value = configString;
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);
                    alert('配置已复制到剪贴板！');
                });
            } catch (error) {
                alert('导出配置失败: ' + error.message);
            }
        }

        // 导入配置
        importConfig() {
            try {
                const configString = this.container.querySelector('#importConfig').value.trim();
                if (!configString) {
                    alert('请输入配置内容！');
                    return;
                }

                this.configManager.importConfig(configString);
                alert('配置导入成功！');
                this.loadConfigToUI();
                // 重新加载配置
                Object.assign(CONFIG, this.configManager.getConfig());
            } catch (error) {
                alert('导入配置失败: ' + error.message);
            }
        }

        // 重置配置
        resetConfig() {
            if (confirm('确定要重置为默认配置吗？这将清除所有自定义设置。')) {
                this.configManager.resetToDefault();
                this.loadConfigToUI();
                alert('配置已重置为默认值！');
                // 重新加载配置
                Object.assign(CONFIG, this.configManager.getConfig());
            }
        }
    }

    // 初始化配置管理器和UI管理器
    const configManager = new ConfigManager();
    const CONFIG = configManager.getConfig();
    const uiManager = new UIManager(configManager);

    // 全局变量：记录已处理的对话，避免重复处理
    let processedConversations = new Set();
    let lastProcessTime = 0; // 记录上次处理时间
    let conversationReplyCount = new Map(); // 记录每个对话的回复次数
    let conversationLastReplyTime = new Map(); // 记录每个对话的最后回复时间
    let emergencyStopCount = 0; // 紧急停止计数器

    // 通过XPath获取元素
    function getElementByXPath(xpath) {
        return document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
    }

    // 通过CSS选择器获取元素
    function getElementBySelector(selector) {
        return document.querySelector(selector);
    }

    // 等待元素出现（支持XPath和CSS选择器）
    function waitForElement(selector, timeout = 10000, isXPath = true) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const checkElement = () => {
                const element = isXPath ? getElementByXPath(selector) : getElementBySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`元素未找到: ${selector}`));
                } else {
                    setTimeout(checkElement, 100);
                }
            };
            checkElement();
        });
    }

    // 模拟点击事件
    function simulateClick(element) {
        if (!element) {
            console.error('点击元素不存在');
            return false;
        }

        const event = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true
        });

        element.dispatchEvent(event);
        console.log('已点击元素:', element);
        return true;
    }

    // 输入文本到元素
    function inputText(element, text) {
        if (!element) {
            console.error('输入元素不存在');
            return false;
        }

        // 聚焦输入框
        element.focus();

        // 清空现有内容
        element.innerHTML = '';

        // 设置新内容
        element.textContent = text;

        // 触发多种事件确保文本被正确识别
        const events = ['input', 'change', 'keyup'];
        events.forEach(eventType => {
            const event = new Event(eventType, { bubbles: true });
            element.dispatchEvent(event);
        });

        console.log('已输入文本:', text);
        return element; // 返回输入框元素供发送使用
    }

    // 智能发送消息（优先使用发送按钮，备选Enter键）
    function sendMessage(inputElement) {
        // 首先尝试找到发送按钮
        for (const selector of CONFIG.sendButtonSelectors) {
            const sendButton = document.querySelector(selector);
            if (sendButton && sendButton.offsetParent !== null) {
                console.log('找到发送按钮:', selector);
                simulateClick(sendButton);
                return true;
            }
        }

        // 如果没有找到发送按钮，使用Enter键
        if (inputElement) {
            console.log('未找到发送按钮，使用Enter键发送');
            const enterEvent = new KeyboardEvent('keydown', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                which: 13,
                bubbles: true,
                cancelable: true
            });
            inputElement.dispatchEvent(enterEvent);
            return true;
        }

        console.error('无法发送消息：未找到发送按钮且输入框无效');
        return false;
    }

    // 获取当前用户名（飞书特定）
    function getCurrentUserName() {
        try {
            // 方法1: 从头像元素获取用户名
            const avatarImg = document.querySelector('.navHeader_avatar img, [class*="avatar"] img, .user-avatar img');
            if (avatarImg && avatarImg.alt) {
                return avatarImg.alt.trim();
            }

            // 方法2: 从用户信息区域获取
            const userNameElement = document.querySelector('[class*="user-name"], [class*="username"], .user-info');
            if (userNameElement) {
                return userNameElement.textContent.trim();
            }

            // 方法3: 从页面标题或其他地方获取
            const titleElement = document.querySelector('title');
            if (titleElement && titleElement.textContent.includes('-')) {
                const parts = titleElement.textContent.split('-');
                if (parts.length > 1) {
                    return parts[0].trim();
                }
            }

            return null;
        } catch (error) {
            console.warn('获取当前用户名时出错:', error);
            return null;
        }
    }

    // 解析消息预览文本（飞书特定）
    function parseMessagePreview(textContent) {
        try {
            // 飞书消息预览格式通常是: "发送者名字: 消息内容" 或 "发送者名字, 群组: 消息内容"
            const colonIndex = textContent.indexOf(':');
            const chineseColonIndex = textContent.indexOf('：');

            let separatorIndex = -1;
            if (colonIndex !== -1 && chineseColonIndex !== -1) {
                separatorIndex = Math.min(colonIndex, chineseColonIndex);
            } else if (colonIndex !== -1) {
                separatorIndex = colonIndex;
            } else if (chineseColonIndex !== -1) {
                separatorIndex = chineseColonIndex;
            }

            if (separatorIndex > 0) {
                const senderPart = textContent.substring(0, separatorIndex).trim();
                const messagePart = textContent.substring(separatorIndex + 1).trim();

                // 处理群组消息格式: "发送者, 群组名"
                let senderName = senderPart;
                if (senderPart.includes(',')) {
                    senderName = senderPart.split(',')[0].trim();
                }

                return {
                    sender: senderName,
                    message: messagePart,
                    isGroup: senderPart.includes(',')
                };
            }

            return {
                sender: null,
                message: textContent,
                isGroup: false
            };
        } catch (error) {
            console.warn('解析消息预览时出错:', error);
            return {
                sender: null,
                message: textContent,
                isGroup: false
            };
        }
    }

    // 查找所有未读消息对话
    // 检查最后一条消息是否来自用户自己
    function isLastMessageFromSelf(conversationElement) {
        try {
            const textContent = conversationElement.textContent || '';

            // 获取当前用户名
            const currentUserName = getCurrentUserName();

            // 解析消息预览
            const messageInfo = parseMessagePreview(textContent);

            // 新增方法0: 检查是否包含自动回复的特定内容
            const autoReplyMessages = [
                CONFIG.replyMessages.default,
                CONFIG.replyMessages.group,
                CONFIG.replyMessages.security
            ];

            for (const replyMsg of autoReplyMessages) {
                if (textContent.includes(replyMsg)) {
                    console.log('检测到自己发送的消息 (方法0 - 自动回复内容匹配):', replyMsg.substring(0, 30));
                    return true;
                }
            }

            // 方法1: 检查消息预览文本中是否包含"我:"或类似标识
            if (textContent.includes('我:') || textContent.includes('You:') || textContent.includes('我：')) {
                console.log('检测到自己发送的消息 (方法1 - 我:标识):', textContent.substring(0, 50));
                return true;
            }

            // 方法2: 通过解析的发送者名字与当前用户名比较
            if (currentUserName && messageInfo.sender) {
                if (messageInfo.sender === currentUserName ||
                    messageInfo.sender.includes(currentUserName) ||
                    currentUserName.includes(messageInfo.sender)) {
                    console.log('检测到自己发送的消息 (方法2 - 用户名匹配):', messageInfo.sender, '==', currentUserName);
                    return true;
                }
            }

            // 方法3: 检查是否是系统消息或自己的操作
            const selfActionKeywords = ['我撤回了', '我删除了', '我发送了', '我分享了', '我上传了'];
            for (const keyword of selfActionKeywords) {
                if (textContent.includes(keyword)) {
                    console.log('检测到自己发送的消息 (方法3 - 操作关键词):', keyword);
                    return true;
                }
            }

            // 方法4: 检查飞书特定的CSS类名和结构
            const feishuSelfIndicators = [
                '[class*="self"]',
                '[class*="own"]',
                '[class*="me"]',
                '[class*="sent"]',
                '[class*="outgoing"]',
                '[class*="right"]',
                '[class*="sender-self"]',
                '[class*="message-self"]',
                '[class*="from-me"]'
            ];

            for (const selector of feishuSelfIndicators) {
                if (conversationElement.querySelector(selector)) {
                    console.log('检测到自己发送的消息 (方法4 - CSS类名):', selector);
                    return true;
                }
            }

            // 方法5: 检查消息状态标识（飞书特有）
            const statusIndicators = [
                '已发送', '已读', '已送达',
                'sent', 'delivered', 'read',
                '✓', '✓✓'  // 飞书的已读标识
            ];

            for (const indicator of statusIndicators) {
                if (textContent.includes(indicator)) {
                    console.log('检测到自己发送的消息 (方法5 - 状态标识):', indicator);
                    return true;
                }
            }

            // 方法6: 检查是否有"草稿"标识
            if (textContent.includes('草稿') || textContent.includes('Draft')) {
                console.log('检测到自己发送的消息 (方法6 - 草稿标识)');
                return true;
            }

            // 方法7: 检查是否包含"撤回了一条消息"等自己的操作
            const selfActionIndicators = [
                '撤回了一条消息',
                '删除了一条消息',
                'recalled a message',
                'deleted a message'
            ];

            for (const indicator of selfActionIndicators) {
                if (textContent.includes(indicator)) {
                    console.log('检测到自己发送的消息 (方法7 - 操作标识):', indicator);
                    return true;
                }
            }

            // 方法8: 检查时间戳位置（自己的消息时间戳通常在右侧）
            const timeElements = conversationElement.querySelectorAll('[class*="time"], [class*="timestamp"]');
            for (const timeEl of timeElements) {
                const style = window.getComputedStyle(timeEl);
                if (style.textAlign === 'right' || style.float === 'right') {
                    console.log('检测到自己发送的消息 (方法8 - 时间戳位置)');
                    return true;
                }
            }

            // 方法9: 检查对话预览中的消息方向标识
            // 飞书中自己发送的消息通常会有特定的标识或格式
            const messagePreview = conversationElement.querySelector('[class*="preview"], [class*="content"], [class*="message"]');
            if (messagePreview) {
                const previewText = messagePreview.textContent || '';
                // 检查是否是以自己的操作开头
                if (previewText.match(/^(我|你|You)\s*[:：]/)) {
                    console.log('检测到自己发送的消息 (方法9 - 预览文本格式)');
                    return true;
                }

                // 检查是否包含发送状态
                if (previewText.match(/(发送中|发送失败|重新发送)/)) {
                    console.log('检测到自己发送的消息 (方法9 - 发送状态)');
                    return true;
                }
            }

            // 方法10: 检查是否有未读数字标识但消息是自己发的
            // 如果有未读标识但最后消息显示是自己发的，则跳过
            const unreadBadge = conversationElement.querySelector('[class*="badge"], [class*="unread"], [class*="count"]');
            if (unreadBadge && textContent.includes('我:')) {
                console.log('检测到自己发送的消息 (方法10 - 未读标识但自己发送)');
                return true;
            }

            // 如果所有检测都未命中，记录调试信息
            if (currentUserName) {
                console.log('未检测到自己发送的消息 - 当前用户:', currentUserName, '消息预览:', messageInfo.sender, ':', messageInfo.message.substring(0, 30));
            } else {
                console.log('未检测到自己发送的消息 - 无法获取当前用户名, 消息预览:', textContent.substring(0, 50));
            }

            return false;
        } catch (error) {
            console.warn('检查消息发送者时出错:', error);
            return false;
        }
    }

    // 检查对话是否设置了免打扰
    function isConversationMuted(conversationElement) {
        try {
            // 方法1: 检查飞书特定的免打扰图标 AlertsOffOutlined
            const alertsOffIcon = conversationElement.querySelector('svg[data-icon="AlertsOffOutlined"]');
            if (alertsOffIcon) {
                return true;
            }

            // 方法2: 检查免打扰图标的父容器类名
            const universeIcon = conversationElement.querySelector('.universe-icon svg[data-icon="AlertsOffOutlined"]');
            if (universeIcon) {
                return true;
            }

            // 方法3: 检查免打扰图标的路径特征（备用方法）
            const svgIcons = conversationElement.querySelectorAll('svg');
            for (const svg of svgIcons) {
                const paths = svg.querySelectorAll('path');
                for (const path of paths) {
                    const pathData = path.getAttribute('d') || '';
                    // 检查是否包含免打扰图标的特征路径（斜线穿过铃铛的图案）
                    if (pathData.includes('M3.217 1.703a1 1 0 0 0-1.414 1.415l19.091 19.09') ||
                        pathData.includes('1.414 1.415l19.091 19.09')) {
                        return true;
                    }
                }
            }

            // 方法4: 检查通用的免打扰相关标识（兼容性）
            const muteIcon = conversationElement.querySelector('[class*="mute"], [class*="silent"], [class*="bell-slash"], [data-testid*="mute"]');
            if (muteIcon) {
                return true;
            }

            // 方法5: 检查免打扰相关的CSS类名
            const classList = conversationElement.className || '';
            if (classList.includes('muted') || classList.includes('silent') || classList.includes('no-disturb')) {
                return true;
            }

            // 方法6: 检查其他可能的免打扰图标属性
            const allIcons = conversationElement.querySelectorAll('svg, i[class*="icon"]');
            for (const icon of allIcons) {
                const iconClass = icon.className || '';
                const iconTitle = icon.getAttribute('title') || '';
                const iconAriaLabel = icon.getAttribute('aria-label') || '';
                const dataIcon = icon.getAttribute('data-icon') || '';

                if (iconClass.includes('mute') || iconClass.includes('silent') ||
                    iconTitle.includes('免打扰') || iconTitle.includes('静音') ||
                    iconAriaLabel.includes('免打扰') || iconAriaLabel.includes('静音') ||
                    dataIcon.toLowerCase().includes('mute') || dataIcon.toLowerCase().includes('silent') ||
                    dataIcon.includes('AlertsOff')) {
                    return true;
                }
            }

            return false;
        } catch (error) {
            console.warn('检查免打扰状态时出错:', error);
            return false;
        }
    }

    function findUnreadConversations() {
        const unreadConversations = [];

        // 查找所有带有未读标识的对话元素
        const conversationElements = document.querySelectorAll('[class*="conversation"], [class*="chat"], [class*="item"]');

        conversationElements.forEach(element => {
            // 检查是否有未读标识
            const hasUnreadBadge = element.querySelector('[class*="badge"], [class*="unread"], [class*="count"], sup') !== null;

            if (hasUnreadBadge) {
                const textContent = element.textContent || '';

                // 检查是否在跳过列表中
                const shouldSkip = CONFIG.skipConversations.some(keyword =>
                    textContent.includes(keyword)
                );

                // 检查最后一条消息是否来自用户自己
                const isFromSelf = isLastMessageFromSelf(element);

                // 检查是否设置了免打扰
                const isMuted = isConversationMuted(element);

                // 生成对话的唯一标识（使用更稳定的方法）
                const conversationTitle = element.querySelector('[class*="title"], [class*="name"], h3, h4') || element;
                const conversationId = conversationTitle.textContent.trim().split('\n')[0].trim() || textContent.trim().substring(0, 50);
                const isAlreadyProcessed = processedConversations.has(conversationId);

                if (!shouldSkip && !isFromSelf && !isMuted && !isAlreadyProcessed) {
                    unreadConversations.push({
                        element: element,
                        text: conversationId,
                        isGroup: textContent.includes(',') || textContent.includes('群'),
                        id: conversationId
                    });
                    console.log('添加待回复对话:', conversationId);
                } else {
                    if (isFromSelf) {
                        console.log('跳过自己发送的未读消息:', conversationId);
                    } else if (isAlreadyProcessed) {
                        console.log('跳过已处理的对话:', conversationId);
                    } else if (isMuted) {
                        console.log('跳过免打扰的对话:', conversationId);
                    } else if (shouldSkip) {
                        console.log('跳过关键词匹配的对话:', conversationId);
                    }
                }
            }
        });

        console.log('找到需要回复的未读对话:', unreadConversations.length, '个');
        return unreadConversations;
    }

    // 处理单个对话的自动回复
    async function replyToConversation(conversation) {
        try {
            console.log('开始回复对话:', conversation.text);

            // 获取最新配置
            const currentConfig = configManager.getConfig();

            // 1. 点击对话
            simulateClick(conversation.element);

            // 等待对话界面加载
            await new Promise(resolve => setTimeout(resolve, currentConfig.actionDelay));

            // 2. 等待输入框出现
            const inputBox = await waitForElement(CONFIG.inputBoxSelector, 5000, false);

            // 3. 选择合适的回复内容
            let replyMessage = CONFIG.replyMessages.default;
            if (conversation.isGroup) {
                replyMessage = CONFIG.replyMessages.group;
            }

            // 4. 输入回复
            const inputElement = inputText(inputBox, replyMessage);
            if (!inputElement) {
                console.error('输入文本失败');
                return false;
            }

            // 等待输入完成
            await new Promise(resolve => setTimeout(resolve, 500));

            // 5. 发送消息
            if (!sendMessage(inputElement)) {
                console.error('发送消息失败');
                return false;
            }

            // 等待消息发送完成（增加等待时间确保状态同步）
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 6. 关闭聊天框，返回消息列表
            try {
                // 尝试点击返回按钮或关闭按钮
                const backButton = document.querySelector('[data-testid="back-button"], .back-button, [aria-label*="返回"], [aria-label*="back"], .close-button');
                if (backButton) {
                    simulateClick(backButton);
                    console.log('已点击返回按钮关闭聊天框');
                } else {
                    // 如果没有找到返回按钮，尝试按ESC键
                    document.dispatchEvent(new KeyboardEvent('keydown', {
                        key: 'Escape',
                        code: 'Escape',
                        keyCode: 27,
                        which: 27,
                        bubbles: true
                    }));
                    console.log('已发送ESC键关闭聊天框');
                }

                // 等待界面切换
                await new Promise(resolve => setTimeout(resolve, 500));
            } catch (closeError) {
                console.warn('关闭聊天框时出错，但不影响回复功能:', closeError);
            }

            console.log('对话回复完成:', conversation.text);
            return true;

        } catch (error) {
            console.error('回复对话时出错:', error);
            return false;
        }
    }

    // 批量处理所有未读消息
    async function processAllUnreadMessages() {
        try {
            const currentTime = Date.now();

            // 紧急停止检查
            if (emergencyStopCount >= CONFIG.emergencyStopThreshold) {
                console.error('触发紧急停止机制，自动回复已暂停');
                CONFIG.enabled = false;
                configManager.updateConfig({ enabled: false });
                return false;
            }

            // 防止频繁处理：如果距离上次处理时间少于10秒，则跳过
            if (currentTime - lastProcessTime < 10000) {
                console.log('距离上次处理时间过短，跳过本次处理');
                return false;
            }

            console.log('开始处理所有未读消息...');
            lastProcessTime = currentTime;

            let successCount = 0;
            let maxRetries = 2; // 减少重试次数
            let retryCount = 0;

            while (retryCount < maxRetries) {
                const unreadConversations = findUnreadConversations();

                if (unreadConversations.length === 0) {
                    console.log('没有新的未读消息需要处理');
                    break;
                }

                console.log(`第${retryCount + 1}轮处理，发现${unreadConversations.length}个新未读对话`);

                for (const conversation of unreadConversations) {
                    const conversationId = conversation.id;

                    // 检查单对话回复次数限制
                    const replyCount = conversationReplyCount.get(conversationId) || 0;
                    const lastReplyTime = conversationLastReplyTime.get(conversationId) || 0;

                    if (replyCount >= CONFIG.maxRepliesPerConversation) {
                        console.log(`对话 ${conversationId} 已达到最大回复次数限制 (${replyCount})`);
                        processedConversations.add(conversationId);
                        continue;
                    }

                    // 检查最小回复间隔
                    if (currentTime - lastReplyTime < CONFIG.minReplyInterval) {
                        console.log(`对话 ${conversationId} 距离上次回复时间过短，跳过`);
                        continue;
                    }

                    const success = await replyToConversation(conversation);
                    if (success) {
                        successCount++;
                        processedConversations.add(conversationId);

                        // 更新回复计数和时间
                        conversationReplyCount.set(conversationId, replyCount + 1);
                        conversationLastReplyTime.set(conversationId, currentTime);

                        console.log(`成功处理对话: ${conversationId} (第${replyCount + 1}次回复)`);

                        // 限制已处理对话记录的数量，避免内存泄漏
                        if (processedConversations.size > 1000) {
                            const oldEntries = Array.from(processedConversations).slice(0, 500);
                            oldEntries.forEach(entry => processedConversations.delete(entry));
                            console.log('清理旧的已处理对话记录');
                        }
                    } else {
                        // 回复失败时增加紧急停止计数
                        emergencyStopCount++;
                        console.warn(`回复失败，紧急停止计数: ${emergencyStopCount}`);
                    }
                }

                retryCount++;

                // 等待一段时间让页面状态更新
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // 重置紧急停止计数器（成功完成一轮处理后）
            if (successCount > 0) {
                emergencyStopCount = 0;
            }

            console.log(`批量回复完成！成功回复 ${successCount} 个对话，总计已处理 ${processedConversations.size} 个不同对话`);
            return successCount > 0;

        } catch (error) {
            console.error('批量处理消息时出错:', error);
            return false;
        }
    }

    // 主监控循环
    function startMonitoring() {
        console.log('开始监控飞书消息...');
        let isProcessing = false; // 防止重复处理
        let lastUnreadCount = 0;
        let monitoringInterval;

        function startMonitoringLoop() {
            // 清除之前的定时器
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
            }

            // 获取最新配置
            const currentConfig = configManager.getConfig();

            monitoringInterval = setInterval(async () => {
                // 每次检查时获取最新配置
                const latestConfig = configManager.getConfig();

                // 检查是否启用自动回复
                if (!latestConfig.enabled) {
                    return;
                }

                // 如果正在处理，跳过本次检查
                if (isProcessing) {
                    console.log('正在处理消息，跳过本次检查');
                    return;
                }

                try {
                    // 检查当前页面是否在消息列表
                    if (!window.location.href.includes('/messenger/')) {
                        console.log('不在消息页面，跳过检查');
                        return;
                    }

                    // 更新全局CONFIG以确保其他函数使用最新配置
                    Object.assign(CONFIG, latestConfig);

                    const unreadConversations = findUnreadConversations();
                    const currentUnreadCount = unreadConversations.length;

                    // 检查是否有未读消息需要处理（改进检查逻辑）
                    if (currentUnreadCount > 0) {
                        // 如果数量发生变化，或者距离上次处理时间超过5分钟，则处理
                        const shouldProcess = currentUnreadCount !== lastUnreadCount ||
                                            (Date.now() - lastProcessTime > 5 * 60 * 1000);

                        if (shouldProcess) {
                            console.log(`检测到 ${currentUnreadCount} 个未读消息，开始自动回复...`);

                            isProcessing = true;
                            const success = await processAllUnreadMessages();

                            if (success) {
                                lastUnreadCount = 0; // 处理成功后重置计数
                                console.log('所有消息处理完成');

                                // 显示通知
                                if (latestConfig.showNotifications) {
                                    if (window.Notification && Notification.permission === 'granted') {
                                        new Notification('飞书自动回复', {
                                            body: `已自动回复 ${currentUnreadCount} 个对话`,
                                            icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIwIDJINEMyLjkgMiAyIDIuOSAyIDRWMjJMMTggMjBIMjBDMjEuMSAyMCAyMiAxOS4xIDIyIDE4VjRDMjIgMi45IDIxLjEgMiAyMCAyWiIgZmlsbD0iIzAwN2JmZiIvPgo8L3N2Zz4K'
                                        });
                                    }
                                }
                            } else {
                                console.log('部分消息处理失败，将在下次检查时重试');
                            }

                            isProcessing = false;
                        }
                    } else if (currentUnreadCount === 0 && lastUnreadCount > 0) {
                        console.log('未读消息已清空');
                        lastUnreadCount = 0;
                    } else if (currentUnreadCount === lastUnreadCount && currentUnreadCount > 0) {
                        console.log(`未读消息数量未变化: ${currentUnreadCount}`);
                    }

                    // 如果检查间隔发生变化，重新启动监控循环
                    if (latestConfig.checkInterval !== currentConfig.checkInterval) {
                        console.log(`检查间隔已更改为 ${latestConfig.checkInterval}ms，重新启动监控`);
                        startMonitoringLoop();
                        return;
                    }

                } catch (error) {
                    console.error('监控过程中出错:', error);
                    isProcessing = false;
                }
            }, currentConfig.checkInterval);
        }

        // 启动监控循环
        startMonitoringLoop();
    }

    // 手动触发回复功能（可在控制台调用）
    window.feishuAutoReply = {
        // 手动处理所有未读消息
        processAll: async function() {
            console.log('手动触发：处理所有未读消息');
            return await processAllUnreadMessages();
        },

        // 查看当前未读消息
        checkUnread: function() {
            const conversations = findUnreadConversations();
            console.log('当前未读消息:', conversations);
            return conversations;
        },

        // 更新配置
        updateConfig: function(newConfig) {
            Object.assign(CONFIG, newConfig);
            console.log('配置已更新:', CONFIG);
        },

        // 获取当前配置
        getConfig: function() {
            return CONFIG;
        }
    };

    // 添加页面可见性检测
    function handleVisibilityChange() {
        if (document.visibilityState === 'visible') {
            console.log('页面变为可见，检查未读消息');
            // 页面变为可见时立即检查一次
            setTimeout(() => {
                const unreadConversations = findUnreadConversations();
                if (unreadConversations.length > 0) {
                    console.log(`页面可见时发现 ${unreadConversations.length} 个未读消息`);
                }
            }, 1000);
        }
    }

    // 等待页面完全加载后开始监控
    function init() {
        console.log('🚀 飞书自动回复助手 v3.0.0 (界面版) 已启动');
        console.log('📋 当前配置:', CONFIG);
        console.log('💡 使用说明:');
        console.log('   - 点击右上角的⚙️按钮或按 Ctrl+Shift+C 打开配置面板');
        console.log('   - 在控制台输入 window.feishuAutoReply.processAll() 手动处理所有未读消息');
        console.log('   - 在控制台输入 window.feishuAutoReply.checkUnread() 查看当前未读消息');
        console.log('   - 在控制台输入 window.feishuAutoReply.updateConfig({key: value}) 更新配置');
        console.log('   - 在控制台输入 window.feishuAutoReply.getConfig() 查看当前配置');

        // 请求通知权限
        if (CONFIG.showNotifications && window.Notification && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    console.log('✅ 通知权限已获取');
                } else {
                    console.log('❌ 通知权限被拒绝');
                }
            });
        }

        // 添加页面可见性监听
        document.addEventListener('visibilitychange', handleVisibilityChange);

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(startMonitoring, 3000);
            });
        } else {
            setTimeout(startMonitoring, 3000);
        }
    }

    // 清理已处理对话记录的函数
    function clearProcessedConversations() {
        processedConversations.clear();
        conversationReplyCount.clear();
        conversationLastReplyTime.clear();
        lastProcessTime = 0;
        emergencyStopCount = 0;
        console.log('已清理所有已处理对话记录和安全计数器');
    }

    // 定期清理过期的已处理记录（每30分钟）
    setInterval(() => {
        if (processedConversations.size > 0) {
            console.log('定期清理已处理对话记录...');
            clearProcessedConversations();
        }
    }, 30 * 60 * 1000);

    // 暴露调试方法到全局
    window.feishuAutoReply = {
        processAll: processAllUnreadMessages,
        checkUnread: findUnreadConversations,
        getConfig: () => CONFIG,
        updateConfig: (updates) => {
            Object.assign(CONFIG, updates);
            configManager.updateConfig(updates);
            console.log('配置已更新:', updates);
        },
        clearProcessed: clearProcessedConversations,
        getProcessedCount: () => processedConversations.size,
        showProcessed: () => Array.from(processedConversations),
        getReplyStats: () => ({
            processedConversations: processedConversations.size,
            replyCount: conversationReplyCount.size,
            emergencyStopCount: emergencyStopCount
        }),
        resetEmergencyStop: () => {
            emergencyStopCount = 0;
            console.log('紧急停止计数器已重置');
        }
    };

    // 启动脚本
    init();

    console.log('飞书自动回复助手已加载完成！');
     console.log('调试方法已暴露到 window.feishuAutoReply');
     console.log('可用方法: processAll(), checkUnread(), getConfig(), updateConfig(), clearProcessed(), getProcessedCount(), showProcessed()');

})();